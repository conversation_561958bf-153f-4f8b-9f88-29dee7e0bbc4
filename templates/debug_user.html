{% extends "base.html" %}
{% block title %}Debug User State{% endblock %}
{% block content %}
<div class="container">
    <article class="round">
        <h4>User Debug Information</h4>
        <p><strong>User:</strong> {{ user }}</p>
        <p><strong>User ID:</strong> {{ user.id|default:"None" }}</p>
        <p><strong>Username:</strong> {{ user.username|default:"Anonymous" }}</p>
        <p><strong>Is Authenticated:</strong> {{ user.is_authenticated }}</p>
        <p><strong>Is Anonymous:</strong> {{ user.is_anonymous }}</p>
        <p><strong>Is Active:</strong> {{ user.is_active|default:"N/A" }}</p>
        <p><strong>Session Key:</strong> {{ request.session.session_key|default:"No session" }}</p>
        
        <h5>Navigation Test</h5>
        {% if user.is_authenticated %}
            <p>✅ User is authenticated - should show logout button</p>
            <a href="{% url 'users:logout' %}">
                <button class="chip">Test Logout</button>
            </a>
        {% else %}
            <p>❌ User is not authenticated - should show login/signup buttons</p>
            <a href="{% url 'users:login' %}">
                <button class="chip">Test Login</button>
            </a>
            <a href="{% url 'users:signup' %}">
                <button class="chip">Test Sign Up</button>
            </a>
        {% endif %}
    </article>
</div>
{% endblock content %}
