from django.views.generic import CreateView
from django.contrib.auth.views import <PERSON>ginView, LogoutView
from django.urls import reverse_lazy
from .forms import SignUpForm


class SignupView(CreateView):
    template_name = "registration/signup.html"
    form_class = SignUpForm
    success_url = reverse_lazy("users:login")


class UserLoginView(LoginView):
    template_name = "registration/login.html"
    redirect_authenticated_user = True


class UserLogoutView(LogoutView):
    """Custom logout view that accepts GET requests"""

    http_method_names = ["get", "post"]
